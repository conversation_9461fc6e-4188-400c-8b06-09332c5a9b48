<style type="text/css">
 .profile{
text-align: center;
margin-top: -33px;
position: absolute;
width: 100%;
left: 0;
right: 0;
 }
 .profile img{
 width: 75px;
 height: 75px;
 border-radius: 50%;
 }
 .profile .h1{
	 margin-bottom: 10px;
 }
 .house{
	 width: 90%;
	 margin-left: 5%;
	 padding-top: 160px;
 }
 .house2{
	 white-space: nowrap;
 }
 .house2 .img2,
 .house2 .img3{
	 width: 49%;
	 display: inline-block;
 }
 .house2 .img3{
	 float: right;
 }
 .houseinfo{
	 width: 90%;
	 margin-left: 5%;
	 padding-top: 15px;
	 line-height: 140%;
	 font-size: 14px;
 }
 .houseinfo img{
	 width: 17px;
	 height: 17px;
	 vertical-align:baseline
 }
 .houseinfo img.left{
	 margin-left: 10px;
 }
 .houseinfo .number{
	 padding-left: 2px;
 }
 .houseinfo .m{
	 font-size: 12px;
	 line-height: 1.3em;
 }
 .QRcode{
	 text-align: right;
	width: 100px;
	float: right;
 }
 .QRcode img{
	 width: 88px;
	 height: 88px;
 }
 .powerImg{
	 vertical-align: top;
	 margin-left: 15px;
	 width: 30px;
	 height: 30px;
	 display: inline-block;
	 margin-top: -8px;
 }
 .bottom{
	 text-align: center;
	 padding-bottom: 30px;
	 vertical-align: top;
	 padding-top: 14px;
 }
 .bottom h4{
	 display: inline-block;
 }
 .h1{
 font-family : Helvetica Neue;
 font-size : 18px;
 color : #231F20;
 }
 h2{
 font-family: Helvetica Neue;
 font-size:17px;
 font-weight:bold;
 letter-spacing : 0.96px;
 color: #E1302F;
 }
 h4{
 font-family: Helvetica Neue;
 font-size:16px;
 color: #BBBDC0;
 }
 #templateWrapper .detail{
 font-family:Helvetica Neue;
 font-size : 14px;
 color : #666766;
 letter-spacing : 0.96px;
 }
 #templateWrapper .detail.cpny{
 margin-bottom: 10px;
 }
 #bgWrapperTop{
   background: #f1f1f1;
	 height: 100%;
 }
 #bgWrapperGradient{
	 height: 270px;
	 background: linear-gradient(to bottom right, #e03131 , #ef873e);
 }
 #templateWrapper {
 font-family: Helvetica Neue;
 font-size:16px;
 color : #666766;
 letter-spacing : 1px;
 line-height:120%;
 background: white;
 margin:0 5%;
 width: 90%;
 box-shadow: #b9b9b9 3px 4px 7px;
 margin-bottom: 80px;
 margin-top: -200px;
 position: relative;
 }
 img{
 max-width: 100%;
 }
 .split{
	 border-top: 1px solid #A7A9AB;
	 width: 90%;
	 margin-left: 5%;
	 height: 10px;
 }
</style>
<div id="bgWrapperTop">
<div id="bgWrapperGradient"></div>
<div id="templateWrapper">
<div class="profile">
   <img src="{{=it.user.avt}}"  referrerpolicy="same-origin"></img>
   <div class="h1">{{! it.user.fnm}}</div>
   <span>{{! it.user.cpny_pstn || ''}}</span>
   <div class="detail">{{! it.user.mbl}}</div>
   <div class="detail cpny">{{! it.user.cpny}}</div>
	 <div class="split"></div>
</div>
<div class="house images">
   <img src="{{=it.prop.img0}}"   referrerpolicy="same-origin"></img>
	 <div class="house2">
		 <img src="{{=it.prop.img1}}"  class="img2" referrerpolicy="same-origin"></img>
	   <img src="{{=it.prop.img2}}"  class="img3" referrerpolicy="same-origin"></img>
	 </div>
</div>
<div class="houseinfo">
   <div class="h1">{{=it.prop.showAddr||it.prop.addr||''}}</div>
   <p>{{=it.prop.city||''}}, {{=it.prop.prov||''}}, {{=it.prop.zip||''}}</p>
   <img src="./rooms icon-01.png" referrerpolicy="same-origin"></img>
	 <span class="number">{{=it.prop.rmbdrm||it.prop.bdrms||it.prop.tbdrms||''}}</span>
   <img src="./rooms icon-02.png" class="left" referrerpolicy="same-origin"></img>
	 <span class="number">{{=it.prop.rmbthrm||it.prop.tbthrms||it.prop.bthrms||''}}</span>
   <img src="./rooms icon-03.png" class="left" referrerpolicy="same-origin"></img>
	 <span class="number">{{=it.prop.rmgr || it.prop.tgr || it.prop.gr ||''}}</span>
	 <div class="QRcode">
      {{?it.user.qrcd}}
      <img src="{{=it.user.qrcd}}" width="115px" height="115px" referrerpolicy="same-origin"></img>
      {{??}}
	    <img src="./app-download.png" width="115px" height="115px" referrerpolicy="same-origin"></img>
      {{?}}
	 </div>
	 {{?it.prop.sqft}}
   <div>{{=it.prop.sqft}} {{-Sqft}}</div>
	 {{?}}
	 <div>ID: {{=it.prop.sid||''}} </div>
   <div>{{-Type}}:  {{=it.prop.ptype2||''}}</div>
	 {{?it.prop.tax}}
	 <div>{{-Tax}}: {{=it.prop.tax}}/{{=it.prop.taxyr||''}}</div>
	 {{?}}
   <h2 style="display:inline-block">{{=it.prop.FormatedLp}}</h2>
   <span style="padding-left:10px; color: #E1302F">{{=it.prop.saleTpTag||''}}</span>
</div>

<div class="bottom">
   <h4>{{-Powered By RealMaster}}</h4>
	 <img src="./icon.png"  class="powerImg" referrerpolicy="same-origin"></img>
</div>


</div>
</div>


