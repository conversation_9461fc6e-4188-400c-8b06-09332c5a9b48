<div id="marketRcmd" class="props">
  {{? it.id != 'owners'}}
  <div class="header">
    <span class="pull-left header-str">{{- Recommend}}</span>
  </div>
  {{?}}
  <div class="mktrcmd top {{? it.appmode == 'mls'}}swip{{?}}">
    {{~it.recos :prop:index}}
    <div class="prop" data-sub="recommend" data-id="{{= (/^RM/.test(prop.id))?prop.id:prop._id}}" onclick="openPopup('/1.5/prop/detail/inapp?lang={{=it.lang}}&id={{= (/^RM/.test(prop.id))?prop.id:prop._id}}&d=/1.5/index','{{- RealMaster}}')">
      <div class="detail">
        <div class="addr one-line">
          {{? prop.addr}}
          <span>{{=prop.unt?prop.unt:''}} {{=prop.showAddr||prop.addr}}</span>
          {{??}}
          <span>{{=prop.city}} {{=prop.prov}} {{=prop.zip}}</span>
          {{?}}
        </div>
        {{? prop.addr}}
        <div class="prov">{{=prop.city}}, {{=prop.prov}}</div>
        {{?}}
        <div class="bdrms">
          {{? prop.bdrms}}
          <span><span class="fa fa-rmbed"></span> {{=prop.bdrms}}{{? prop.br_plus}}+{{=prop.br_plus}}{{??}}{{?}}</span>
          {{?}}
          {{? prop.rmbthrm || prop.tbthrms || prop.bthrms}}
          <span><span class="fa fa-rmbath"></span> {{=prop.rmbthrm || prop.tbthrms || prop.bthrms || ''}}</span>
          {{?}}
          {{? prop.rmgr || prop.tgr || prop.gr}}
          <span><span class="fa fa-rmcar"></span> {{= prop.rmgr || prop.tgr || prop.gr ||''}}</span>
          {{?}}
        </div>
        <div class="price">{{= prop.priceValStrRed || prop.lp}}</div>
        {{? prop.ltp == 'exlisting' && (it.rcmdCity)}}
        <div class="exlinks" onclick="showMoreProps(event,{o:'{{=it.rcmdCity.o}}',p:'{{=it.rcmdCity.p}}',pn:'{{=it.rcmdCity.pn}}',n:'{{=it.rcmdCity.n}}'},{tp:'exlisting'})" data-sub="more exlisting recommend" data-query="city:{{=it.rcmdCity.n}}">{{- Exclusive}}</div>
        {{?}}
        {{? prop.ltp == 'assignment' && (it.rcmdCity)}}
        <div class="exlinks" onclick="showMoreProps(event,{o:'{{=it.rcmdCity.o}}',p:'{{=it.rcmdCity.p}}',pn:'{{=it.rcmdCity.pn}}',n:'{{=it.rcmdCity.n}}'},{tp:'assignment'})" data-sub="more assignment recommend" data-query="city:{{=it.rcmdCity.n}}">{{- Assignment}}</div>
        {{?}}
        {{? (prop.ltp == 'rent') && (!prop.cmstn) && (it.rcmdCity)}}
        <div class="exlinks" onclick="showMoreProps(event,{o:'{{=it.rcmdCity.o}}',p:'{{=it.rcmdCity.p}}',pn:'{{=it.rcmdCity.pn}}',n:'{{=it.rcmdCity.n}}'},{tp:'rent'})" data-sub="more landlord rent recommend" data-query="city:{{=it.rcmdCity.n}}">{{- Landlord Rental}}</div>
        {{?}}
        {{? (prop.ltp == 'rent') && (prop.cmstn) && (it.rcmdCity)}}
        <div class="exlinks" onclick="showMoreProps(event,{o:'{{=it.rcmdCity.o}}',p:'{{=it.rcmdCity.p}}',pn:'{{=it.rcmdCity.pn}}',n:'{{=it.rcmdCity.n}}'},{tp:'exrent'})" data-sub="more exclusive rent recommend" data-query="city:{{=it.rcmdCity.n}}">{{- Exclusive Rental}}</div>
        {{?}}
        {{? prop.ohdate}}
        <div class="oh"><span class="fa fa-rmhistory"></span>{{- Open House}}: {{=prop.ohdate}}</div>
        {{?}}
      </div>
      <div class="img">
        <img class="lazy" data-src={{=prop.thumbUrl || '/img/noPic.png'}} referrerpolicy="same-origin" />
        <div class="tag">
          {{? prop.ytvid || prop.vurlcn}}
          <span class="vid"><span class="fa fa-youtube-play"></span></span>
          {{?}}
          {{? prop.ltp == 'exlisting'}}
          <span class="ltp">{{- Exclusive}}</span>
          {{?}}
          {{? prop.ltp == 'assignment'}}
          <span class="ltp">{{- Assignment}}</span>
          {{?}}
          {{? prop.ltp == 'rent'}}
          <span class="ltp">{{- Rental}}</span>
          {{?}}
          {{? prop.type}}
          <span class="type">{{=prop.type}}</span>
          {{?}}
        </div>
      </div>
    </div>
    {{~}}
  </div>
</div>