<!-- this page is used in app, opened in map search, can share and open school detail -->
<template lang="pug">
div
  prop-fav-actions(:loading="loading", :disp-var="dispVar")
  prop-showing-actions(:disp-var="dispVar")
  condo-description

  page-spinner(:loading="loading")
  share-dialog(:w-dl="wDl", :w-sign="wSign", :disp-var="dispVar", :prop="prop", :no-advance="noAdvance")
  prop-detail-map-menu(:prop="prop", :disp-var="dispVar")

  flash-message
  header.bar.bar-nav(style="padding-right:0px;", v-if="mode == 'list'")
    h1.title {{_("RealMaster")}} {{items.length}} {{_('Results')}}

  //- div#schoolDetailModal.modal(v-show="mode == 'school'")
  //-   school-detail(:no-bar='false')

  div#list-container.content(v-show="items.length && mode == 'list'")
    prop-list(:list.sync='items', :disp-var="dispVar")
  prop-need-login(:no-bar="true", :redirect="true", v-show="mode == 'error'")
  sign-up-form-contact-realtor(:prop="prop",
    :formStatus="formStatus", 
    :brkg="prop.adrltr",
    :feedurl="feedurl",
    :owner="owner", 
    :user-form="userForm", 
    :title="signupTitle", 
    @click.prevent.stop="noop($event)",
    :disp-var='dispVar')
  div#propDetailModal(v-show="mode=='detail'")
    prop-detail(:w-id="wSign", 
      :owner="dispVar.ownerData", 
      :user-form="userForm", 
      :show-close='items.length > 0', 
      :from-share="false", :in-frame="inFrame", 
      :no-title-bar="items.length < 1",
      :showShowingIcon='showShowingIcon')

  brkg-phone-list(:disp-var="dispVar",v-if="dispVar.isApp")
  rm-brkg-phone-list(v-if="dispVar.isApp", :cur-brkg="curBrkg")
  div.WSBridge(style="display:none", v-if="!items.length")
    listing-share-desc(:prop="prop", :is-app="dispVar.isApp")
    div#share-image {{shareImage}}
    div#share-data {{shareData}}
    span#share-url {{fullUrl}}
//- div.WSBridge(style="display:none", v-if="items.length")
//-   div#wx-title {{_('RealMaster')}} {{items.length}} {{_('Results')}}
//-   div#wx-desc
//-     span(v-if="dispVar.ownerData.eml")
//-       | {{_('Shared by')}} {{dispVar.ownerData.fn?dispVar.ownerData.fn+dispVar.ownerData.ln:dispVar.ownerData.nm}},{{dispVar.ownerData.mbl}},{{dispVar.ownerData.eml}}
//-     span(v-else)
//-       | {{_('RealMaster Multiple Properties Sharing')}}
//-   div#wx-image
//-     span(v-if="dispVar.ownerData.eml")
//-       | {{dispVar.ownerData.avt || 'https://realmaster.cn/img/logo.png'}}
//-     span(v-else)
//-       | http://realmaster.cn/img/logo.png
//-   div#wx-url {{fullUrl}}
//-   span#wx-url {{fullUrl}}
</template>

<script>
import PropList  from './frac/PropList.vue'
// import SchoolDetail from './frac/SchoolDetail.vue'
import PropDetail from './frac/PropDetail.vue'
import ListingShareDesc from './frac/ListingShareDesc.vue'

import PageSpinner from './frac/PageSpinner.vue'
import PropNeedLogin from './frac/PropNeedLogin.vue'
import BrkgPhoneList  from './frac/BrkgPhoneList.vue'
import RmBrkgPhoneList  from './frac/RmBrkgPhoneList.vue'

import ShareDialog from './frac/ShareDialog2.vue'
import FlashMessage from './frac/FlashMessage.vue'
import PropFavActions from './frac/PropFavActions.vue'
import SignUpFormContactRealtor from './frac/SignUpFormContactRealtor.vue'
import PropDetailMapMenu from './frac/PropDetailMapMenu.vue'
import PropShowingActions  from './showing/propShowingActions.vue'

import rmsrv_mixins from './rmsrv_mixins'
import pagedata_mixins from './pagedata_mixins'
import prop_mixins from './prop_mixins'
import contactRealtor_mixins from './contactRealtor_mixins'
import condoDescription from './frac/CondoDescription.vue'
export default {
  mixins: [pagedata_mixins, rmsrv_mixins, prop_mixins,contactRealtor_mixins],
  data () {
    return {
      dispVar: {
        defaultEmail:'',
        lang:        'en',
        ownerData:   {},
        allowedShareSignProp:false,
        isApp:       false,
        isLoggedIn:  false,
        sessionUser: {},
        allowedEditGrpName:false,
        listShareMode:false,
        shareLinks: {l:[],v:[]},
        projShareUID:'',
        rltrTopAd:false,
        isRealtor:false
      },
      mode:        'detail',
      loading: true,
      // fullUrl:     document.URL,
      prop:        {
        avgSld:0,
        avgS:0,
        avgR:0,
        avgRtd:0
      },
      qrcd:        false,
      wSign:       true,
      wDl:         true,
      inFrame:     false,
      schVueClick: false, //just jump page
      id:          this.parseVarsId(),
      signupTitle: this._('Book a Tour'),
      items:       [],
      locale:      'zh-cn',
      owner:  {vip:1},
      userForm:{
        ueml:'<EMAIL>',
        formid:'system',
        tp:'prop',
        sid:'',
        nm:'',
        fn:'',
        ln:'',
        eml:'',
        mbl:'',
        projShareUID:''
      },
      forceShowSignup:false,
      page:'appPropDetailPage',
      datas:[
        'defaultEmail',
        'isCip',
        'isApp',
        // 'jsGmapUrl',
        'sessionUser',
        'isVipUser',
        'isVipRealtor',
        'isRealtor',
        'isLoggedIn',
        'lang',
        'allowedShareSignProp',
        'allowedPromoteProp',
        'hasFollowedRealtor',
        'shareUID',
        'coreVer',
        // 'gmapGeocode',
        'allowedEditGrpName',
        'reqHost',
        'shareAvt',
        'shareLinks',
        'rltrTopAd',
        'shareHost',
      ],
      datasObj:{
        src:'detail',
        url:document.URL,
        page:'mapSearch'
      },
      noAdvance:false,
      feedurl: '/1.5/form/forminput',
      showShowingIcon:true,
    };
  },
  computed:{
    fullUrl:function(){
      if(this.dispVar.isApp){
        // /toronto-on/1234-toronto-st/unt-community-(sid|rm1-id)
        return `${this.dispVar.reqHost}/1.5/prop/detail?id=${this.prop._id}&wDl=1`
        // let prop = this.prop || {};
        // return `${this.dispVar.shareHost}/${prop.city_en}-${prop.prov_abbr}/${prop.addr}/${encodeURIComponent(prop.cmty)}-${prop._id}`
      }
      return document.URL;
    },
    showSignupModal:function () {
      if (this.forceShowSignup) {
        return true;
      }
      return (this.prop.rmcontact && !this.dispVar.isRealtor) || /^RM/.test(this.prop.id)
    },
    curBrkg: function () {
      if (this.prop.ltp == 'rent' && !this.prop.cmstn) {
        return this.prop.adrltr;
      }
      var userFollowedRltr = this.dispVar.userFollowedRltr;
      if (userFollowedRltr && userFollowedRltr._id) {
        return userFollowedRltr;
      }
      if (this.prop.adrltr) {
        return this.prop.adrltr;
      }
      // if (this.prop.uid) {
      //   return this.userDict[this.prop.uid] || {};
      // }
      return {}
    },
    shareImage: function () {
      return this.prop.thumbUrl ? this.prop.thumbUrl : '/img/create_exlisting.png';
    },
    shareData: function () {
      var prop = this.prop, lang = this.dispVar.lang;
      var propID;
      if (this.isRMProp(prop)) {
        propID = prop.id;
      } else {
        propID = prop._id || prop.sid;
      }
      var shareUID = this.dispVar.shareUID;
      var data_text = `id=${propID}&tp=listing&lang=${lang}`
      if (this.isRMProp(prop)) {
        // data_text += '&ownerid='+prop.uid;
        // user(not realtor) not following, use id
        if (!shareUID) {
          shareUID = prop.uid;
        }
      }
      if (this.dispVar.allowedShareSignProp) {
        if (this.wDl) {
          data_text += '&wDl=1';
        }
        if (this.wSign) {
          data_text += '&aid=' + this.dispVar.shareUID;
        }
      } else {
        data_text += "&wDl=1";
        if (this.dispVar.isLoggedIn) {
          data_text += "&uid=" + shareUID;
        }
      }
      // force append download
      if (this.isRMProp(prop)) {
        if (!/wDl=1/.test(data_text)) {
          data_text += '&wDl=1';
        }
        data_text += "&wSign=1";
      }
      return data_text;
    }
  },
  beforeMount (){
    if (vars.lang) {
      this.locale = vars.lang;
    }
    if (vars.mode) {
      this.mapMode = vars.mode;
    }
    if (vars.showShowingIcon) {
      // 如果存在该字段表明是从showingDetail打开的
      this.showShowingIcon = false;
    }
    // this.wDl = vars.wDl?true:false;
    this.inFrame = (vars.inframe || vars.inFrame)?true:false;
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    if (!window.gMapsCallback) {
      window.gMapsCallback = this.initGmap;
    }
    var bus = window.bus, self = this;
    bus.$on('valute-modify-from-child', function (d) {
      let fld = d.fld;
      let v = d.v;
      self[fld] = v;
    });
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      if (d.sessionUser && d.sessionUser.eml) {
        for (let i of ['nm','eml','mbl','fnm','fn','ln']) {
          self.userForm[i] = d.sessionUser[i];
        }
        if(!self.userForm.nm || self.userForm.fnm){
          self.userForm.nm = self.userForm.fnm || d.sessionUser.nm_en || d.sessionUser.nm_zh
        }
      }
      if (d.projShareUID) {
        self.userForm.projShareUID = d.projShareUID;
      }
      if (RMSrv.hasWechat){
        RMSrv.hasWechat((has)=>{
          if (has!=null) {
            self.userForm.hasWechat = has;
          }
        })
      }
    });
    // bus.$on('school-retrieved', function (sch) {
    //   self.mode = 'school';
    //   toggleModal('schoolDetailModal','open');
    // })
    bus.$on('school-close', function (sch) {
      self.mode = 'detail';
    })
    bus.$on('school-changed', function (sch) {
      var cfg = {hide:false, title:this._('RealMaster')};
      var url = self.appendDomain('/1.5/school/public/detail?id='+sch._id);
      RMSrv.getPageContent(url, '#callBackString', cfg, function(val){
        if (val == ':cancel') {
          console.log('canceled');
          return;
        }
        try {
          // var val = 'loc=43.5723199141038,-79.5785565078259&zoom=15&saletp=lease';
          if (/^cmd-redirect:/.test(val)) {
            // return alert(val);
            var url = val.split('cmd-redirect:')[1];
            return window.location = url;
          }
          var callBackStr = ':ctx:'+val
          // alert(callBackStr);
          window.rmCall(callBackStr);
          // var d = self.urlParamToObject(val);
          // window.bus.$emit('school-prop', d);
        } catch (e) {
          console.error(e);
        }
        // console.log('returned city');
      });
    });
    bus.$on('school-prop', function (d) {
      var url = '/1.5/mapSearch', self = this;
      if (d.sch && d.sch.loc) {
        url += '?loc=' + d.sch.loc[0] + ',' + d.sch.loc[1];
        url += '&zoom=15';
        url += '&saletp='+d.type;
      }
      var callBackStr = ':ctx:'+url.split('?')[1];
      // console.log(callBackStr);
      window.rmCall(callBackStr);
      // return window.location = url;
    });
    bus.$on('set-loading',function(val){
      let tmp = val?true:false;
      self.loading = tmp;
    });
    bus.$on('prop-retrived', function (prop) {
      self.mode = 'detail';
      self.loading = false;
      if (prop.e) {
        self.mode = 'error';
        if (prop.ec == 1 || prop.needLogin) {
          // self.message = prop.e;
          bus.$emit('prop-need-login', prop.e);
        }
      } else {
        self.formatedAddr = `${prop.showAddr||prop.addr}, ${prop.city_en||prop.city}`
        if (/^RM/.test(prop.id)) {
          self.userForm.id = self.userForm.rmid = prop.id;
        } else {
          self.userForm.id = self.userForm.sid = prop._id;
        }
        if (!prop.thumbUrl) {
          prop.thumbUrl = self.picUrl(prop);
        }
        self.prop = prop;
      }
    });
    bus.$on('prop-detail-close', function (prop) {
      self.mode = 'list';
    });
    bus.$on('get-form-input-status', function (ret) {
      self.getFormInputProps(ret.eml);
    });
    if (!self.datasObj.ownerId) {
      delete self.datasObj.ownerId;
      // self.datas.pop();
    }
    self.getPageData(self.datas, self.datasObj, true);
    if (self.dispVar.defaultEmail){
      self.userForm.ueml = self.dispVar.defaultEmail;
    }
    self.initData();
    this.noAdvance = vars.showShareIcon?true:false;
  },
  components: {
    PropDetail,
    ListingShareDesc,
    PropFavActions,
    ShareDialog,
    FlashMessage,
    PageSpinner,
    BrkgPhoneList,
    RmBrkgPhoneList,
    PropNeedLogin,
    PropList,
    // SchoolDetail,
    SignUpFormContactRealtor,
    PropDetailMapMenu,
    PropShowingActions,
    condoDescription
  },
  methods: {
    parseVarsId(){
      if (vars.id){
        if(/,/.test(vars.id)){
          return vars.id.split(',')
        } else {
          return [vars.id]
        }
      }
      return []
    },
    noop(e){
      if (e) {
        e.preventDefault();
        e.stopPropagation();
        return 0;
      }
    },
    toggleModal (a,b,c) {
      toggleModal(a, b);
      if (c) {
        toggleDrop()
      }
    },
    closeAndRedirect(url){
      // console.log(url);
      var self = this;
      if (!/^(http|https)/.test(url)) {
        url = self.appendDomain(url);
      }
      // if (RMSrv.closeAndRedirectRoot) {
      //   RMSrv.closeAndRedirectRoot(url);
      // } else {
      // NOTE: Parent was open using get getPageContent
      // NOTE: default to true, since list page have handler too
      var fromNativeMap = true
      if(vars.mode == 'list' && vars.src =='pn'){
        fromNativeMap = false;
      }
      // alert(fromNativeMap)
      // NOTE: from pn /fav page /eval
      if(!fromNativeMap){
        return window.location = url;
      } else {
        var callBackStr = ':ctx:redirect:'+url;
        window.rmCall(callBackStr)
      }
      // }
    },
    initData(){
      var self = this;
      self.loading = true;
      if (self.id.length > 1) {
        self.mode = 'list';
        self.getItems();
      } else {
        window.bus.$emit('prop-changed', {_id:self.id[0], locale:self.locale});
      }
    },
    getItems(){
      var self = this;
      self.$http.post('/1.5/search/prop/list', {id:self.id.join(','), share:true, locale:self.locale}).then(
        function(ret) {
          ret = ret.data;
          self.loading = false;
          if (ret.e) {
            console.error(ret.e);
            self.err = ret.e;
            return;
          }
          self.err = '';
          if (ret.resultList) {
            self.items = self.items.concat(ret.resultList);
          }
          self.initPropListImg();
        }, function(ret) {
          ajaxError(ret);
        });
    },
    // calc school distance
    initGmap () {
      var map = new google.maps.Map(document.getElementById('detail-map-holder'), {
          center: {lat: -34.397, lng: 150.644},
          zoom: 8
      });
    },
    initPropListImg () {
      for (let prop of this.items) {
        if (!prop.thumbUrl) {
          prop.thumbUrl = this.picUrl(prop);
        }
      }
    },
    // isRMProp (prop) {
    //   return /^RM/.test(prop.id);
    //   // if (!id) {
    //   //   return false;
    //   // }
    //   // return id.substr(0, 3) === 'RM1';
    // },
    picUrl (r) {
      var ret = this.setupThisPicUrls(r);
      return ret[0] || (window.location.origin + "/img/noPic.png");
      // if (this.isRMProp(r)) {
      //   r.pic.ml_num = r.sid || r.ml_num;
      //   ret = this.convert_rm_imgs(this, r.pic, 'reset');
      //   return ret[0] || (window.location.origin + "/img/noPic.png");
      // } else {
      //   return listingPicUrls(r, this.dispVar.isCip)[0] || '/img/noPic.png';
      // }
    },
    showSchool (_id) {
    },
    openTBrowser (url) {
      window.location = url;
    },
    showInBrowser(url){
      window.location = url;
    },
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
#SignupModal .close{
    width: 100%;
    text-align: center;
    padding-top: 30px;
}
#SignupModal .close img {
    width: 40px;
    height: 40px;
}
#prpNeedLogin.modal{
  display: block;
  height: 100%;
  opacity: 1;
  /*transition: transform .25s;*/
  transform: translate3d(0,0,0px);
}
</style>
