<template lang="pug">
div.prop(@click="propClicked()")
  div.detail
    div.address {{prop.unt||''}} {{prop.showAddr||prop.addr}}
    div.prov(style="color:#999; font-size:12px") {{prop.city}}, {{prop.prov}}
    div.bdrms
      span(v-show="prop.rmbdrm != null || prop.bdrms != null || prop.tbdrms != null")
        span.fa.fa-rmbed
        span {{prop.rmbdrm || prop.tbdrms || prop.bdrms}}
      span(v-show="prop.rmbthrm != null || prop.tbthrms != null || prop.bthrms != null")
        span.fa.fa-rmbath
        span {{prop.rmbthrm || prop.tbthrms || prop.bthrms}}
      span(v-show="prop.rmgr != null || prop.gr != null || prop.tgr != null")
        span.fa.fa-rmcar
        span {{prop.rmgr || prop.tgr || prop.gr}}
    div.price
      div(v-if="soldOrLeased")
        span {{(prop.sp) | currency('$', 0)}}
      div
        span(:class="{'through':soldOrLeased}") {{(prop.lp || prop.lpr) | currency('$', 0)}}
        span.txt(v-show="soldOrLeased") {{_('Asking Price')}}
  div.img-wrapper
    div.img
      img(referrer-policy="no-referrer",:src="computedBgImg",  onerror="this.src='/img/noPic.png';return true;")
      span.ts {{formatTs(prop.slddt || prop.mt)}}
      div.stp(:class="prop.tagColor || 'gray'",v-show="prop.saleTpTag")
        | {{prop.saleTpTag}}
</template>

<script>
import rmsrv_mixins from '../rmsrv_mixins'
import evaluate_mixins from '../evaluate_mixins'
import filters from '../filters'

export default {
  mixins: [rmsrv_mixins,evaluate_mixins],
  filters:{
    currency:filters.currency,
  },
  props: {
    dispVar:{
      type:Object,
      default:function () {
        return {fav:false};
      }
    },
    fromMls: {
      type: Boolean,
      default: function () {
        return false
      }
    },
    prop: {
      type: Object,
      default: function () {
        return {}
      }
    },
    saleOrRent: {
      type: String,
      default:function() {
        return ''
      }
    },
    showIcon:{
      type: String,
      default: function () {
        return ''
      }
    }
  },
  computed:{
    dist:function() {
      var d = this.prop.dist;
      if(d>=1000) {
        return parseFloat(d/1000).toFixed(1) + 'km';
      } else {
        return d+'m';
      }
    },
    soldOrLeased: function () {
      return this.prop.saleTpTag_en == 'Sold'|| ['Sld','Lsd','Pnd','Cld'].includes(this.prop.lst);
    },
    computedSaletp:function(){
      let saletp = this.prop.saletp;
      if (saletp) {
        if (Array.isArray(saletp)) {
          return saletp.join(' ')
        } else {
          return saletp;
        }
      }
      return this.prop.lpunt;
    },
    saletpIsSale: function () {
      if (Array.isArray(this.prop.saletp_en)) {
        return /Sale/.test(this.prop.saletp_en.join(','));
      }
      return /Sale/.test(this.prop.saletp_en.toString());
    },
    computedBgImg: function () {
      if (this.prop && this.prop.thumbUrl) {
        return this.prop.thumbUrl;
      }
      return '/img/noPic.png';
    },
    computedHeight: function() {
      if (this.fromMls) {
         return 105;
      }
      var height = 105;
      if (this.prop.sqft) {
        height = height +25;
      }
      if (this.prop.depth) {
        height = height + 25;
      }
      if (this.prop.dist) {
        height = height + 25;
      }
      if (this.showIcon && height < 145)
        height = 145;
      return height;
    },
    propSid:function () {
      if (this.prop.sid) {
        return this.prop.sid;
      } else {
        return this.prop._id?this.prop._id.substr(3):'';
      }
    }
  },
  data () {
    return {
    };
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
  },
  methods: {
    formatTs(ts) {
      return formatDate(ts);
    },
    propClicked() {
      this.openPropPage(this.prop);
    },
  }
}
</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.content {
  padding-bottom: 44px;
}

.price .txt {
  font-size:12px;
  color: #666;
  padding-left: 10px;

}
.img-wrapper .img{
  width: 130px;
  /* display: table-cell; */
  vertical-align: top;
  padding-top: 0px;
  position: relative;
}
.img-wrapper .img img{
  display: inline-block;
  width: 100%;
  height: 83px;
}
.img-wrapper {
  height:100%;
  position:relative;
}
.img-wrapper >div {
  position:relative;
}
.img-wrapper .img >div {
  color: white;
  width: auto;
  text-align: center;
  position: absolute;
  top: 0;
  right: 0;
  padding: 0px 4px 0px;
  /* margin-top: -28px; */
  height: 19px;
  font-size: 12px;
  background: rgba(30,166,27,0.9);
}
.img-wrapper .ts {
  color: #fff;
  text-align: center;
  position: absolute;
  bottom: 10px;
  left: 10px;
  font-size: 10px;
  border-radius: 25px;
  background: black;
  opacity: .7;
  padding: 2px 10px 3px;
  line-height: 12px;
}

.computedHeight {
  height: 145px;
}
.equal{
  font-family: Arial;
  padding-left: 2px;
}
.green {
  color:#42c02e;
}
.red {
  color:#e03131;
}
.address {
  padding-left: 10px;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 100%;
  overflow: hidden;
  display: block;
  font-weight: 500;
}
.bdrms span{
  padding-right: 2px;
  background: #f1f1f1;
  color:#428bca;
  padding-left: 2px;
  margin-right: 3px;
}
.sqft span {
  padding: 0px;
}

.bdrms, .sqft, .dist{
  font-size: 12px;
  color: #777;
  padding-bottom: 10px;
}
.prop .action {
  color: #42c02e;
  border: #5cb85c;
  padding-top: 10px;
  width: 100%;
}
.prop .action > span {
  display: flex;
  align-content: center;
  justify-content: center;
}
.prop .action .btn {
  border-radius: 0;
  border: 1px solid #42c02e;
  font-size: 12px;
  color: #42c02e;
  padding: 7px 15px;
}
.prop .detail {
  display: inline-block;
  width: calc(100% - 90px);
  float: left;
  font-size: 15px;
}
.prop .detail>div {
  padding: 1px 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.prop {
  background: white;
  width: 100%;
  position: relative;
  border-bottom: 1px solid #f1f1f1;
  display: flex;
  align-items: end;
  padding: 10px 10px 10px 0px;
}

.prop .price{
  padding: 1px 10px;
  color:#666;
  font-size: 14px;
}

.price div:first-of-type {
  font-size: 17px!important;
  color: #e03131!important;
}

.prop .price .through{
  text-decoration: line-through;
}
.prop .stp.sold{
  background: #e03131;
  opacity: 0.9
}
.prop .stp.inactive{
  background: #07aff9;
}
.nopadding{
  padding:0px!important;
}
.prop .addr, .prop .bdrms, .prop .sid{
  padding: 1px 10px;
}
.prop .bdrms{
  font-size: 12px;
  color: #777;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.prop .addr{
  font-size: 15px;
  padding-top: 10px;
}
.price.blur{
  filter: blur(2px);
  -webkit-filter: blur(2px);
}
.img-wrapper .red{
  background: #e03131 !important;
  color: #fff !important;
}
.img-wrapper .green{
  background: rgba(30, 166, 27, .9) !important;
  color: #fff !important;
}
.img-wrapper .gray{
  background: gray !important;
  color: #fff !important;
}
</style>
