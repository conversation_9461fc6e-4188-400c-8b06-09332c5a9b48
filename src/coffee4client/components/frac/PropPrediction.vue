<template lang="pug">
div
  header.bar.bar-nav(style="padding-right:0px;", v-if="!noTitleBar")
    h1.title {{_("RealMaster")}}
  div#detail-map-holder
  div#contentWrapper.content(style="background-color: rgb(230, 230, 230);", :class="{oldVerBrowser:oldVerBrowser}")
    div.prop-heading-wrapper
      prop-preview-bottom(:prop.sync='prop')
      div.slider(v-if="!picUrls || picUrls.length <= 0")
        div.slide-group
          div.slide(:style="{ height:computedImgHeight + 'px'}")
            img.wxImg(src='/img/noPic.png')
      //- data-index not supported in swipe
      swipe(:style="{ height:computedImgHeight + 'px'}", v-show="picUrls.length" class="my-swipe", :show-indicators='false')
        swipe-item(v-for="(url,$index) in picUrls", :key="$index")
          img(src="/img/Banner-wechat-ch.png",
            :dataindex="$index",
            :src="url",
            onerror="hanndleImgUrlError(this)")
    div.card.predict_price_card
      div.card-header {{_('AI-Estimate','prediction')}}
        span.predict_mt.pull-right(v-if="prop.pred_mt") {{prop.pred_mt}} {{_('Updated','Prediction')}}
      h1.predict_sp {{prop.pred_sp}}
      div.predict_accuracy
        b {{_('Estimate Range')}}:
        span {{_('Property market value is within this range')}}:
        span.percentage 68%
        //- div.predict_accuracy_container
        //-   span {{_('Accuracy','Prediction')}}:
        //-   span.rate {{_(prop.pred_accuracy,'Prediction')}}
      div.predict_bar
        span.predict_spl {{prop.pred_spl}}
        span.predict_splw {{_('Low','Prediction')}}
        span.predict_sph {{prop.pred_sph}}
        span.predict_sphw {{_('High','Prediction')}}
      //- div.predict_range {{_('Prediction Range','Prediction')}}
      div.prop_price
        div
          span {{prop.lp|currency('$', 0)}} {{_('Listing','Prediction')}}
            span(v-if="prop.onD") {{addDashToDate(prop.onD)}}
        div(v-if="prop.sp")
          span {{prop.sp|currency('$', 0)}} {{_('Sold','Prediction')}}
            span(v-if="prop.sldd") {{addDashToDate(prop.sldd)}}
      h2.evaluation_title {{_('Not sure about AI-Estimate?')}}
      h5.evaluation_subtitle {{_('AI-Estimate vs. Manual Estimate')}}
      a.evaluation_button(@click="goEvaluation()")
        span {{_('Try Manual Estimate')}}
      div.evaluation_hist_title
        div.pull-spinner(v-show="!gotHists",style="display:block;")
        div(v-show="gotHists") {{sprintf(_("%d people estimated",'evaluation'), userCount)}}
      div.evaluation_hist(v-for="hist in hists", v-if="hist.result.p", @click="openHist(hist)")
        div.avt.inline(:style="{'background-image': computedImg(hist)}")
        div.inline
          div.ts {{hist.ts.substr(0,10)}}
          div.price {{ hist.result.p| currency('$', 0)}}
        div.inline.icon.icon-right-nav
      div.predict_msg
        div.inline-block.icon
          span.fa.fa-exclamation-circle
        div.inline-block.desc
          p {{_('This prediction result is NOT suitable as a reference for decision making.','Prediction')}}
          p {{_('If you find an issue ','Prediction')}}
            a(@click='reportError($event)') {{_('Report Error')}}

    #SignupModal.modal.modal-fade
      header.bar.bar-nav
        h1.title
      .content
        sign-up-form(:owner='owner', :feedurl='feedurl', :user-form='userForm', :title='signupTitle', @click.prevent.stop='noop(e)')
        .close(@click="toggleModal('SignupModal')")
          img(src='/img/staging/close.png')
    div.card(v-show="prop.pred_his")
      div.card-header {{_('AI-Estimate History')}}
      canvas(id="chart", height="200")
    // div.card.evaluation_card
    //   h2.evaluation_title {{_('Customize Evaluation')}}
    //   h5.evaluation_subtitle {{_('Evaluate this listing manually')}}
    //   a.evaluation_button(@click="goEvaluation()")
    //     span.fa.fa-line-chart
    //     span {{_('Manual Evaluation')}}
    div.card.predict-card(v-show="prop.pred_sp && dispVar.isAllowedPredict")
      div.card-header
        span {{_('Predict Analysis')}}
        a.pull-right(v-show="dispVar.isAllowedPredict",
          @click="showPredictList()",
          style="color:#428BCA;")
          span.icon-desc {{_('Edit Predict List')}}
          span.icon.icon-right-nav
      div.card-content
        div.predict-vals
          div.sp pred_sp: {{prop.pred_sp}}
          div.std pred_spsd: {{prop.pred_spsd}}
          div.calcs
            div 68% {{prop.pred_sp-prop.pred_spsd}} - {{prop.pred_sp+prop.pred_spsd}}
            div 95% {{prop.pred_sp-2*prop.pred_spsd}} - {{prop.pred_sp+2*prop.pred_spsd}}
          div.canvas
          div.pred_his
            div(v-for="h in prop.pred_his")
              span {{h.ts | dotdate}} {{h.sp}} {{h.std}}
        div.error
          div.err(v-show="prop.pred_err") {{prop.pred_err}}
          div.ok.fa.fa-check-circle(v-show="prop.pred_ok")
          div.input(v-show="showPredictErrorInput")
            input(type="text", placeholder="error detail", v-model="predError")
          div.btn.btn-negative(@click="showPredictErrorInput = true") {{_('Report Error')}}
          div.btn.btn-positive(@click="savePredictError(1)") {{_('Set Predict OK')}}
          div.btn.btn-primary(v-show="showPredictErrorInput && predError" @click="savePredictError()") {{_('Save Err')}}
    div.card.realprediction_card
      div.card-header {{_('RealMaster AI-Estimate')}}
      //- h2.realprediction_title
      //-   span.real REAL
      //-   span Prediction
      //-   sup.logo_ai AI
      h4 {{_('What is AI-Estimate?','Prediction')}}
      p {{_('The AI-Estimate is RealMaster\'s estimated market value, calculated using AI technology. It is not an appraisal. The AI-Estimate is derived from public data, with respect to special features, location, and market conditions. We encourage buyers, sellers, and homeowners to supplement RealMaster\'s information by doing other research such as:')}}
      p {{_('Comparing estimate values between AI-Estimate and Manual Estimate; receiving a comparative market analysis (CMA) from a real estate agent; Getting an appraisal from a professional appraiser;')}}
      h4 {{_('AI-Estimate Accuracy','Prediction')}}
      p {{_('The AI-Estimate’s accuracy depends on the location and availability of data of a certain area. The more data available, the more accurate the AI-Estimate value.')}}
      p
        img(src="/img/prediction/prediction_accuracy.jpg",style="width:100%;")
      p {{_('EP：Estimated price.')}}
      p {{_('SP：Actual sold price.')}}
      p {{_('n: The total properties sold in a specific area within the last 15 days.')}}
      p {{_('This percentage is normally higher than the median number because outlier estimation will make the average difference bigger, but has a little effect on the median.')}}
      h4 {{_('Data Coverage and AI-Estimate Accuracy Table','Prediction')}}
      p {{_('Be aware that in some areas, we might not be able to produce a AI-Estimate. The tables below show you where we have AI-Estimate.')}}

      div.table(v-if="predictionStat && dispVar.isAllowedPredict")
        div.table-section-left
          div.row.header
            div.table-city {{_('City')}}
          div.row(v-for="c in predictionStat.list")
            div.table-city
              div.table-city-title {{c.c}}
              div.table-count count {{c.count}}
        div.table-section-right
          div.row.header
            div.table-type.fixed-width
            div.table-date(v-for="d in predictionStat.dates") {{d}}
          div.row(v-for="c in predictionStat.list")
            div.table-type.fixed-width
              div STDP
              div DIFFP
              div S/L
            template(v-for="d in predictionStat.dates")
              template(v-for="l in c.l")
                div.table-type(v-if="l.dt == d")
                  div.table-percentage
                    span(v-if="l.stdp") {{(l.stdp * 100).toFixed(2)}}%
                  div.table-percentage
                    span(v-if="l.diffp") {{(l.diffp * 100).toFixed(2)}}%
                  div.table-percentage
                    span(v-if="l.sldaskp") {{(l.sldaskp*100).toFixed(2)}}%
      table.table(v-if="!dispVar.isAllowedPredict && predictionStat.length > 0")
        thead
          tr
            th {{_('CITY')}}
            th {{_('ACCURACY','Prediction')}}
        tbody
          tr(v-for="city in predictionStat")
            th {{city._id.city}}
            th
              span(v-if="city.diffp") {{(100 - (city.diffp * 100)).toFixed(2)}}%
  div(style="display:none")
    | {{_('VIP Only')}}
    | {{_('Error when getting translate')}}
</template>

<script>
import SignUpForm from './SignUpForm.vue'
import Swipe from './swipe.vue'
import SwipeItem from './swipe-item.vue'
import PropPreviewBottom from './PropPreviewBottom.vue'

import filters from '../filters'

import pagedata_mixins from '../pagedata_mixins'
import prop_mixins from '../prop_mixins'
import rmsrv_mixins from '../rmsrv_mixins'
// import html2canvas from 'html2canvas';
export default {
  filters:{
    currency:filters.currency,
    dotdate:filters.dotdate,
  },
  mixins:[pagedata_mixins,rmsrv_mixins,prop_mixins],
  props:{
    loading:{
      type:Boolean,
      default:false
    },
    // owner:{
    //   type:Object
    // },
    inFrame:{
      type:Boolean,
      default:false
    },
    noTitleBar:{
      type:Boolean,
      default:false
    },
  },
  data () {
    return {
      myChart:null,
      dispVar:    {
        defaultEmail:'',
        isCip:false,
        isRealtor:false,
        isApp:false,
        lang:'zh',
        isVipRealtor:false,
        isDevGroup:false,
        isAdmin: false,
        rltrTopAd:false,
        isAllowedPredict:false,
      }, //shared between childs
      oldVerBrowser: window.oldVerBrowser || false,
      picUrls:    [],
      mlang:       'En',
      translating: false,
      showPredictErrorInput:false,
      predError:'',
      owner:  {vip:1},
      signupTitle: this._('Report Error'),
      feedurl: '/1.5/form/forminput',
      userForm:{
        formid:'system',
        tp:'predictReport',
        ueml:'<EMAIL>',
        sid:'',
        nm:'',
        eml:'',
        mbl:'',
        src:'',
        m:'Report error from prediction detail page'
      },
      datas:[
        'defaultEmail',
        'isCip',
        'isRealtor',
        'isVipUser',
        'isVisitor',
        'isApp',
        'isLoggedIn',
        'isAllowedPredict',
        // 'gmapStatic',
        'exMapURL',
        'lang',
        'rltrTopAd',
        // 'allowedPromoteProp',
        'isVipRealtor',
        'isTrebUser',
        'isTrebUser2',
        'hasFollowedRealtor',
        'hasFollowedVipRealtor',
        'userFollowedRltr',
        'isDevGroup',
        // 'canShowSoldPrice',
        // 'showSoldPriceBtn',
        'isPaytop',
        // 'isPaytopAll',
        // 'appVerNum',
        'coreVer',
        'isAdmin'
        // 'jsGmapUrl'
      ],
      prop:       this.$parent.prop || {},
      // inFrame has no close btn and bottom nav
      predictionStat: vars.result,
      hists: [],
      gotHists:false,
      users:{},
      userCount: 0,
      histcnt: 0
    };
  },
  computed: {
    computedImgHeight(){
      return (window.innerWidth || 375) / 1.53;
    },
    // isRMProp(){
    //   return /^RM/.test(this.prop.id);
    // },
    propDom:function () {
      if (this.prop && this.prop.dom != null) {
        return this.prop.dom;
      } else {
        return '';
      }
    },
  },
  mounted () {
    var self = this, bus = window.bus;
    //after getPageData
    bus.$on('prop-changed', function (prop) {
      if (prop._id && prop._id == self.prop._id) {
        self.picUrls = this.setupThisPicUrls(prop);
        // if (self.isRMProp) {
        //   self.picUrls = self.convert_rm_imgs(self, self.prop.pic, 'reset');
        // } else {
        //   self.picUrls = listingPicUrls(self.prop, self.dispVar.isCip);
        // }
        // self.picUrls = listingPicUrls(self.prop, self.dispVar.isCip);
        bus.$emit('prop-retrived', self.prop);
        return;
      }
      bus.$emit('clear-cache');
      window.bus.$emit('reset-signup',null);
      self.prop = vars.prop;
      bus.$emit('prop-retrived', vars.prop);
    });
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      self.initProp();
    });
    self.getPageData(self.datas,{},true);
    if (this.dispVar.defaultEmail){
      self.userForm.ueml = self.dispVar.defaultEmail;
    }
    this.contentWrapper = document.getElementById('contentWrapper');
    this.contentWrapper.addEventListener("scroll", this.scrollListener);
  },
  beforeMount (){
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
  },
  components: {
    Swipe,
    SwipeItem,
    SignUpForm,
    PropPreviewBottom
  },
  methods: {
    computedImg:function(hist) {
      const sessionUser = this.dispVar.sessionUser;
      const uid = hist.uid
      if((sessionUser && (uid.toString()==sessionUser._id.toString()) || this.dispVar.isAdmin)){
        if (this.users[uid] && this.users[uid].avt) {
          return 'url('+this.users[uid].avt+')';
        } else {
          return  'url(/img/user-icon-placeholder.png)'
        }
      } else {
        return 'url(/img/user-icon-placeholder.png)'
      }
    },
    handleHists() {
      var self = this;
      if (self.gettingHists || self.gotHists || self.hists.length) {
        return;
      }
      self.gettingHists = true;
      this.$http.post('/1.5/evaluation/hist',{uaddr:self.prop.uaddr,inPredPage:true}).then(
        function(ret) {
          self.gettingHists = false;
          ret = ret.data
          if (ret.ok == 1) {
            self.gotHists = true;
            if (ret.evaluate) {
              self.users = ret.evaluate.users;
              self.hists = ret.evaluate.hist.splice(0,100);
              self.address =  ret.evaluate.addr + ',' + ret.evaluate.city + ',' + ret.evaluate.prov;
            }
            let users = [];
            for (let i = 0; i < self.hists.length; i++) {
              let hist = self.hists[i];
              if (users.indexOf(hist.uid) == -1) {
                users.push(hist.uid)
              }
            }
            self.userCount = users.length;
            self.hists.sort(function(a, b){
              return new Date(b.ts) - new Date(a.ts)
            });
          } else {
            self.gotHists = true;
            console.log(ret.e)
            self.msg = "error";
          }
        },function(ret){
          ajaxError(ret);
        });
    },
    openHist(hist) {
      var url = 'http://' + this.dispVar.reqHost + '/1.5/evaluation/result.html?inframe=1&hist=1&uaddr=' + encodeURIComponent(this.prop.uaddr) + '&id=' + hist._id;
      if (vars.fromMls) {
        url=url+"&fromMls=1";
      }
      if(this.dispVar.isApp) {
        RMSrv.openTBrowser(url, {nojump:true, title:this._('Estimate Report','evaluation')});
      } else {
        window.document.location.href = url;
      }
    },
    reportError(e) {
      e.preventDefault();
      e.stopPropagation();
      this.toggleModal('SignupModal','open')
    },
    toggleModal (a,b,c) {
      toggleModal(a, b);
      if (c) {
        toggleDrop()
      }
    },
    noop(e){
      if (e) {
        e.preventDefault();
        e.stopPropagation();
        return 0;
      }
    },
    showPredictList(){
      this.redirect('/1.5/prop/predlist');
    },
    redirect(url){
      if (this.$parent.closeAndRedirect) {
        url = this.appendDomain(url);
        this.$parent.closeAndRedirect(url);
        return;
      }
      return window.location = url;
    },
    savePredictError(isOk){
      var self = this;
      // console.log(self.predError);
      // return;
      var params = {
        id:this.prop._id,
        err:this.predError,
      }
      if (isOk) {
        params.pred_ok = 1
      }
      self.$http.post('/1.5/prop/updatepredict',params).then(
        function(ret) {
          ret = ret.data;
          if (ret.ok == 1) {
            RMSrv.dialogAlert(ret.msg);
          } else {
            RMSrv.dialogAlert(ret.err);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
     );
    },
    goEvaluation() {
      if (!this.dispVar.isApp) {
        return window.location = '/adPage/needAPP';
      }
      if (!this.dispVar.isLoggedIn) {
        var url = '/1.5/user/login#index';
        url = this.appendDomain(url);
        return RMSrv.closeAndRedirectRoot(url)
      }
      let sqft = this.getPropSqft(this.prop);
      var containAny = function(arr, arrC) {
        var el = arr.find(function(e) {
          for (let str of arrC) {
            if (e.indexOf(str)>=0) {
              return true;
            }
          }
        });
        return el;
      }
      var type;
      if (this.prop.ptype2_en) {
        if (containAny(this.prop.ptype2_en, ['Semi-Detached','1/2 Duplex'])) {
          type = 'Semi-Detached';
        } else if (containAny(this.prop.ptype2_en, ['Townhouse','Stacked'])) {
          type = 'Townhouse';
        } else if (containAny(this.prop.ptype2_en, ['Apartment','Condo'])) {
          type = 'Apartment';
        } else {
          type='Detached';
        }
      }

      var arr = []
      var fields = ['lp','lng','lat','addr','st','bdrms','bthrms','tp','br_plus','gr','unt','sqft1','sqft2','depth','front_ft','lotsz_code','irreg'];
      for (let key of fields) {
        if (this.prop[key]) {
          arr.push(key+'='+this.prop[key]);
        }
      }
      arr.push('tax='+ parseInt(this.prop.tax));
      arr.push('cmty='+this.prop.cmty_en);
      arr.push('city='+this.prop.city_en);
      arr.push('prov='+this.prop.prov_en);
      arr.push('cnty='+this.prop.cnty_en);

      var url = '/1.5/evaluation/evaluatePage.html?nobar=1&inframe=1&fromMls=1&' + arr.join('&') +'&mlsid='+(this.prop._id||'')+'&sqft='+(sqft||'')+'&tp='+type+'&img='+(this.getImgEncodeUrl()||'');
      url = this.appendDomain(url);
      RMSrv.getPageContent(url,'#callBackString', {hide:false, title: this._('Evaluation Conditions','evaluation')}, function(val) {
        if (val == ':cancel') {
          return;
        }
      });
    },
    scrollListener(){
      // console.log(this.contentWrapper.scrollTop,this.dispVar.isApp && this.dispVar.lang !== 'en' && this.prop.status=='A');
      var self = this;
      var value = this.contentWrapper.scrollTop;
      if (value > 500) {
        if (!window.Chart) {
          // RMSrv.dialogAlert('Chart not loaded yet')
          console.error('Chart not loaded yet');
          return;
        }
      }
    },
    appendDomain(url){
      // var tmp    = document.createElement ('a');
      // tmp.href   = document.URL;
      // var domain = tmp.hostname;
      var location = window.location.href;
      var arr = location.split("/");
      var domain = arr[0] + "//" + arr[2];
      url = domain + url;
      return url;
    },
    getImgEncodeUrl(){
      if (!this.picUrls.length) {
        return null
      }
      if (this.picUrls[0]) {
        return encodeURIComponent(this.picUrls[0])
      }
      return null;
    },
    redirect(url){
      if (this.$parent.closeAndRedirect) {
        url = this.appendDomain(url);
        this.$parent.closeAndRedirect(url);
        return;
      }
      return window.location = url;
    },
    initProp () {
      var ml_num  = vars.ml_num || this.prop.sid || this.prop._id;
      if (!ml_num) {
        return;
      }
      var self = this;
      this.picUrls = this.setupThisPicUrls(this.prop);
      // if (this.isRMProp) {
      //   if (self.prop.pic) {
      //     self.prop.pic.ml_num = self.prop.sid || self.prop.ml_num;
      //   }
      //   this.picUrls = self.convert_rm_imgs(self, self.prop.pic, 'reset');
      // } else {
      //   this.picUrls = listingPicUrls(this.prop, this.dispVar.isCip);
      if (this.picUrls.length > 0) {
        this.picUrls = [this.picUrls[0]]
      }
      // }
      if (this.prop.pred_mt) {
        this.prop.pred_mt = this.prop.pred_mt.substr(0,10);
      } else {
        if (this.prop.pred_his && this.prop.pred_his.length > 0) {
          const lastIndex = this.prop.pred_his.length - 1
          this.prop.pred_mt = this.prop.pred_his[lastIndex].ts.substr(0,10)
        }
      }
      if (this.prop.pred_his) {
        this.drawChart();
      }
      const user = self.dispVar.sessionUser;
      if (user) {
        self.userForm.id =self.userForm.sid= self.prop.sid
        self.userForm.url = document.URL;
        self.userForm.nm = user.nm;
        self.userForm.eml = user.eml;
        self.userForm.mbl = user.mbl;
        // self.userForm.addr = self.prop.addr;
        var prop = self.prop
        self.userForm.addr = `${prop.unt?(prop.unt+' '):''}${prop.showAddr||prop.addr}`;
        self.userForm.city = self.prop.city_en;
        self.userForm.prov = self.prop.prov_en;
        self.userForm.src = 'Property Id: ' + self.prop._id + ' from predition detail page'
      }
      if (this.prop.uaddr) {
        self.handleHists();
      }
    },
    addDashToDate(date) {
      let dateStr = date.toString();
      return dateStr.slice(0,4)+'-'+dateStr.slice(4,6)+'-'+dateStr.slice(6,8);
    },
    drawChart() {
      this.ctx = document.getElementById('chart');
      this.ctx.width = window.innerWidth - 20; //parseInt(window.innerWidth*mul) || 640;
      this.ctx.height = 200;
      let data = {};
      let datah = [];
      let datal = [];
      let dates = [];
      if (this.prop.pred_his) {
        let length = this.prop.pred_his.length;
        let count = 0;
        for (let i = length - 1; i >= 0; i--) {
          const hist = this.prop.pred_his[i];
          const date = hist.ts.substr(0,10);
          if (dates.indexOf(date) == -1 && count < 10) {
            dates.push(date);
            if (data.hasOwnProperty(date) == false) {
              data[date] = [hist.sp-(hist.std*2),hist.sp,hist.sp+(hist.std*2)]
            }
            count ++;
          }
        }
        data = Object.values(data);
        dates = dates.reverse();
        data = data.reverse();
      }
      var scatterChartData = {
        labels: dates,
        datasets: [{
          backgroundColor: 'rgba(255,0,0,0.5)',
          borderColor: 'red',
          borderWidth: 1,
          outlierColor: '#999999',
          padding: 10,
          itemRadius: 0,
          outlierColor: '#999999',
          label: 'Price Value',
          data: data
        }
        ]
      };
      const self = this;
      window.myScatter = new Chart(this.ctx, {
        type: 'boxplot',
        data: scatterChartData,
        options: {
          legend: {
            display: false
          },
          tooltips: {
            callbacks: {
              label: function(tooltipItem, data) {
                const stats = data.datasets[0].data[tooltipItem.index];
                const mid = self.formatPrice(stats[1]);
                const low = self.formatPrice(stats[0]);
                const high = self.formatPrice(stats[2]);
                return 'Mid:'+mid+'('+low+'~'+high+')';
              }
            }
          },
          scales: {
            xAxes:[{
              barThickness: 15
            }],
            // xAxes: [{
            //   ticks: {
            //     fontSize: 8,
            //     callback: function(value, index, values) {
            //       return value;
            //     }
            //   },
            //   position: 'bottom',
            //   type: 'time',
            //   unit: 'day',
            //   time: {
            //     format: 'YYYY-MM-DD',
            //     displayFormats: {
            //       day: 'D MMM YYYY'
            //     }
            //   }
            // }],
            yAxes: [{
              ticks: {
                callback: function(value, index, values) {
                  return self.formatPrice(value);
                }
              }
            }]
          }
        }
      });
    },
    formatPrice(value) {
      let u = 0
      while (value >= 1000) {
        value = Math.round(value / 10)
        value = value / 100
        u++
      }
      return value + ' KMT'.charAt(u)
    },
    translate_m (prop,cfg={}) {
      var self = this;
      var setLang = function(fld,m) {
        self.mlang = self.mlang === 'En' ? 'Zh' : 'En';
        self.translating = false;
        var tmp = {};
        tmp[fld] = m;
        self.prop = Object.assign({}, prop, tmp);
      };
      if (prop.m_zh && self.mlang == 'En') {
        return self.mlang = 'Zh';
        // return setLang('m',prop.m_zh);
      }
      if (prop.m_zh && self.mlang == 'Zh') {
        return self.mlang = 'En';
        // return setLang('m',prop.m_zh);
      }
      async function _doTranslate_m(idx) {
        if (idx + '' !== '2') {
          return;
        }
        self.translating = true;
        
        if (prop.m_zh) {
          return setLang('m_zh', prop.m_zh);
        }

        try {
          // Await the result of the HTTP POST request
          const response = await self.$http.post('/1.5/prop/pretranslate', { id: prop._id, m: prop.m });
          const ret = response.data;

          if (ret.ok && ret.m_zh) {
            // If translation is already available, set the language
            return setLang('m_zh', ret.m_zh);
          } else if (ret.m) {
            // Await the result of the translation service
            const gret = await RMSrv.getTranslate(ret.m);

            if (gret && gret !== '') {
              await self.$http.post('/1.5/prop/update', { id: prop._id, m_zh: gret.m, src: gret.src });
              setLang('m_zh', gret.m);
              return;
            }

            RMSrv.dialogAlert(self._('Error when getting translate'));
          }
          // else {
          //   self.translating = false;
          // }
        } catch (error) {
          ajaxError(error);
        } finally {
          self.translating = false;
        }
      }
      var optTip = '"Remarks” is automatically translated by Google Translate. Sellers,Listing agents, RealMaster, Canadian Real Estate Association and relevant Real Estate Boards do not provide any translation version and cannot guarantee the accuracy of the translation. In case of a discrepancy, the English original will prevail.';
      var fn = this._?this._:this.$parent._;
      var tip = fn(optTip);
      var later = fn('Cancel');
      var seemore = fn('Translate');
      var optTl = "";
      // _doTranslate_m(2)
      return RMSrv.dialogConfirm(tip, _doTranslate_m, optTl, [later, seemore]);
    },
    openTBrowser (url, cfg={}) {
      if (!cfg.title) {
        cfg.title = this._('RealMaster');
      }
      if (/transtax|mortgage|direction|rentorbuy/.test(url)) {
        url+='&lang='+this.dispVar.lang;
        url = this.appendDomain(url);
      }
      if (cfg.nojump && this.dispVar.isApp) {
        RMSrv.getPageContent(url,'#callBackString', cfg, function(val) {
          if (val == ':cancel') {
            return;
          }
        });
        // return RMSrv.openTBrowser(url, cfg);
      }
      if (this.$parent.openTBrowser) {
        return this.$parent.openTBrowser(url, cfg);
      }
      return window.location = url;
    },
    appendDomain(url){
      var location = window.location.href;
      var arr = location.split("/");
      var domain = arr[0] + "//" + arr[2];
      url = domain + url;
      return url;
    },
  }
}
</script>
<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
@font-face {
  font-family: 'Timeburner';
  src: url('../../../webroot/public/fonts/timeburnernormal.ttf?v=4.4.0') format('truetype');
  font-weight: normal;
  font-style: normal;
}
.inline {
  display: inline-block;
  vertical-align: middle;
}
.my-swipe img{
  width: 100%;
  height: 100%;
}
.card{
  margin: 0px;
  border-radius: 0px;
  position: relative;
}
.card .card-header {
  padding: 0px 0 8px 0 !important;
  margin: 6px 0 20px 0;
  font-weight: bold;
  border: none !important;
}
.card:not(:first-child) {
  margin-top: 10px;
}
.predict-card .card-header .pull-right .icon {
  font-size: 14px;
  color: #666;
  padding-left: 0px;
  vertical-align: top;
  padding-top: 3px;
}
.prop-wrapper {
  margin-top: 0;
  bottom: 0;
}
.slider {
  margin-bottom: 0;
}
.slider img {
  display: block;
  padding: 0;
  color:#ffffff;
  width: 100%;
  height: 100%;
}
[v-cloak] { display:none !important; }
.card {
  padding: 10px;
}
.predict_price_card {
  margin-top: 0 !important;
}
.predict_sp {
  font-size: 22px;
  text-align: center;
  color: #f51c00;
  margin-bottom: 10px;
}
.predict_mt {
  font-size: 12px;
  font-weight: normal;
  color: #888;
  text-transform: capitalize;
}
.predict_bar {
  /* background-image: linear-gradient(120deg,#fff4f4,#f51c00); */
  background-image: radial-gradient(circle,#f51c00,#efd9d9);
  height: 10px;
  width: 90%;
  margin: auto;
  position: relative;
  border-radius: 10px;
}
.predict_bar span {
  position: absolute;
  color: #463e3e;
}
.predict_spl,.predict_sph {
  top: -20px;
}
.predict_splw,.predict_sphw {
  bottom: -20px;
  font-size: 10px;
  text-transform: uppercase;
}
.predict_spl,.predict_splw {
  left: 0;
}
.predict_sph,.predict_sphw {
  right: 0;
}
/* .predict_range {
  text-align: center;
  margin-top: 10px;
  margin-bottom: 10px;
  font-weight: bold;
} */
.prop_price {
  width: 90%;
  margin: auto;
  color: #888;
  margin-top: 40px;
}
.price_red {
  color: #f51e00;
}
.predict_accuracy {
  width:90%;
  margin: 0 auto 40px;
}
.predict_accuracy .percentage {
  color:#f51c00;
}
/* .predict_accuracy_container {
  border: 2px solid #e8e8e8;
  border-radius: 25px;
  margin: auto;
  padding: 5px 10px;
  display: inline-block;
  font-weight: bold;
} */
.predict_accuracy .rate {
  color: #f51e00;
}
.predict_msg {
  background: #f6f6f6;
  padding: 10px;
  margin-top: 20px;
}
.predict_msg .icon {
  width: 10%;
  font-size: 16px;
  color: #f51c00;
  vertical-align: top;
}
.predict_msg .desc {
  width: 90%;
}
.predict_msg .desc p {
  font-size: 12px;
  margin-bottom: 0;
  line-height: 1.3;
}
.evaluation_title {
  text-align: center;
  font-size: 17px;
  margin: 30px 0 0 0;
  color: #2d2d2d;
}
.evaluation_subtitle {
  text-transform: capitalize;
  text-align: center;
  font-size: 12px;
  color: #777;
  padding-top: 5px;
  font-weight: normal;
  margin-top: 5px;
  margin-bottom: 20px;
}
.evaluation_button {
  background-color: #5cb85c;
  padding: 10px 0;
  text-align: center;
  display: block;
  color: #fff;
  margin: 20px 0;
  width:90%;
  margin:auto;
}
.evaluation_hist_title {
  text-align:center;
  margin-top:25px;
}
.evaluation_hist {
  width: 90%;
  margin: 15px auto 0;
  position: relative;
}
.evaluation_hist .price {
  color: #f51c00;
  font-size: 18px;
}
.evaluation_hist .icon-right-nav {
  font-size: 14px;
  position:absolute;
  top:50%;
  transform: translateY(-50%);
  right:0;
}
.evaluation_hists {
  width: 90%;
  margin: auto;
}
.avt {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  z-index: 2;
  margin-right: 10px;
  background-size: 100% 100%;
  background-color: #eaebec;
}
.evaluation_button .fa {
  margin-right: 5px;
}
.realprediction_title {
  text-align: center;
  color: #5d5d5d;
  font-size: 18px;
  margin-top: 20px;
}
.realprediction_title .real {
  color: #f53f2f;
}
.logo_ai {
  background: #f53f2f;
  color: #fff;
  font-size: 7px;
  padding: 1px;
  top: -9px;
  border-radius: 2px;
}
.realprediction_card h4 {
  font-size: 14px;
  color: #2d2d2d;
  margin: 20px 0 5px 0;
  line-height: 18px;
}
.realprediction_card p {
  font-size: 12px;
  color: #7e7e7e;
  margin-top: 10px;
}
.table {
  margin: 20px 0 0 0;
  position: relative;
}
.table thead {
  background: #f7f7f7;
}
.table tr {
  border: 1px solid #ebebeb;
}
.table th {
  border: 1px solid #ebebeb;
  padding: 5px;
  font-weight: normal;
  font-size: 12px;
  color:#000;
}
.table thead th {
  text-transform: uppercase;
  font-weight: bold;
}
.table tbody tr {
  padding: 5px;
}
.table tbody tr:nth-child(even) {
  background: #ebebeb;
}
.table-section-left,
.table-section-right,
.table-city,.table-type,.table-date
 {
  display: inline-block;
  vertical-align: middle;
}
.table-section-left {
  width: 40%;
}
.table-section-right {
  width: 60%;
  overflow-x: scroll;
  white-space: nowrap;
  position: absolute;
  right: 0;
}
.table-city {
  width: 100%;
  height: 84px;
}
.table-city-title {
  overflow-x: scroll;
  white-space: nowrap;
}
.table-count {
  color:#8c8282;
  font-size: 12px;
}
.header .table-type,
.header .table-city {
  height: 43px;
}
.table-type,.table-date {
  width: 85px;
}
.table-type.fixed-width {
  width: 50px;
}
.table-city,.table-type,.table-date{
  padding: 10px 5px;
  border: 1px solid #ebebeb;
  border-right: none;
  border-bottom: none;
}
.table-type:last-child,
.table-date:last-child {
  border-right: 1px solid #ebebeb;
}
.table-percentage {
  width: 73px;
  height: 21px;
}
#SignupModal {
  background-color: rgba(0,0,0,.88);
  z-index: 22;
}
#SignupModal .bar.bar-nav {
  background-color: transparent;
  border-bottom: none;
}
#SignupModal .content {
  background-color:transparent;
}
#SignupModal .close {
  width: 100%;
  text-align: center;
  padding-top: 30px;
}
#SignupModal .close img {
  width: 40px;
  height: 40px;
}
</style>
