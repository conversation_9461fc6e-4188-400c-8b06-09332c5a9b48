<template lang="pug">
div.prop-wrapper#propPreviewBottom(:class="{'soldprop':soldOrLeased && prop.sp}")
  div.prop
    span.nm(v-if="prop.nmOrig || prop.nm || prop.nm_en")
      span(v-if="dispVar.lang == 'en'") {{prop.nmOrig || prop.nm_en || prop.nm}}
      span(v-else) {{prop.nmOrig || prop.nm || prop.nm_en}}
    span.price(v-if="prop.showSoldPrice && prop.sp && prop.status_en !== 'A'") {{prop.sp | currency('$', 0)}}
      span(style="font-size:12px;padding-left:5px") {{_('Sold Price')}}
  div.prop
    div.desc(v-if="prop.desc||prop.desc_en")
      span(v-if="dispVar.lang == 'en'") {{prop.desc_en || prop.desc}}
      span(v-else) {{prop.desc || prop.desc_en}}
    div.pull-left
      span.price(v-show="prop.lp", :class="{'through':soldOrLeased}") {{prop.lp || prop.lpr | currency('$',0)}}
      span.dom(v-show="prop.dom != null && !prop.marketRmProp") {{_('DOM','prop')}}: {{propDom}}
    div.bdrms.pull-right
      span.rmbed(v-show='prop.bdrms')
        span.fa.fa-rmbed
        span.bold  {{prop.bdrms}} {{prop.br_plus?'+ '+prop.br_plus:''}}

      span(v-show='prop.rmbthrm || prop.tbthrms || prop.bthrms')
        span.fa.fa-rmbath
        span.bold  {{prop.rmbthrm || prop.tbthrms|| prop.bthrms}}

      span(v-show='prop.rmgr || prop.tgr || prop.gr')
        span.fa.fa-rmcar
        span.bold  {{prop.rmgr || prop.tgr || prop.gr}}
  div.tax(v-show="prop.tax") {{_('Tax')}}: {{prop.tax}} / {{prop.taxyr}}
  div
    div.trim
      span.addr(v-show="prop.showAddr||prop.addr") {{prop.unt?prop.unt:''}} {{prop.showAddr||prop.addr}} {{prop.apt_num}}
        span(style="font-size:11px;") &#183; {{prop.city}} {{prop.prov}}
      span.ptype {{prop.ptype}} {{prop.ptype2?prop.ptype2.join(' '):''}}
    div.sid2.pull-right(v-if="prop.sid") {{prop.sid}}
</template>

<script>
import filters from '../filters'

export default {
  filters:{
    currency:filters.currency,
  },
  props: {
    dispVar:{
      type: Object,
      default: function () {
        return {}
      }
    },
    prop: {
      type: Object,
      default: function () {
        return {}
      }
    },
    adrltr: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  components:{

  },
  computed:{
    soldOrLeased: function () {
      return this.prop.saleTpTag_en == 'Sold'|| ['Sld','Lsd','Pnd','Cld'].includes(this.prop.lst);
    },
    propDom:function () {
      if (this.prop && this.prop.dom != null) {
        return this.prop.dom;
      } else {
        return '';
      }
    },
  },
  data () {
    return {
      loaderGif:require('../../../webroot/public/img/ajax-loader.gif'),
      rcmdHeight:170,
    };
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
  },
  methods: {
  }
}
</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.prop-wrapper {
  z-index: 2;
  padding: 20px 10px 5px 10px;
  background: linear-gradient(transparent, #000000);
  color: white;
  margin-top: -85px;
  position: absolute;
  height: 90px;
  width: 100%;
  font-size: 12px;
  .nm {
    font-size: 16px;
    font-weight: bold;
  }
  .desc {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1; /* number of lines to show */
    -webkit-box-orient: vertical;
  }
}
.prop-wrapper.soldprop {
  margin-top: -105px;
  height: 110px;
}
.bold {
  font-weight: 500;
  padding-right: 5px;
}
.price {
  color: white;
  padding-right: 5px;
  font-weight: 500;
  font-size: 19px;
}
.price.through {
  text-decoration: line-through;
  font-size: 12px;
}
.dom {
  color: #f9c730!important;
}
.prop-wrapper >div {
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: 19px;
  width: 100%;
}
.sid2{
  padding: 0px 5px;
}
.header .ad {
  margin-right: 10px;
}
.header .ad, .header .oh {
  text-align: center;
  font-size: 10px;
  border-radius: 25px;
  color: white!important;
  padding: 3px 10px 3px;
  line-height: 12px;
}
.header .ad {
  background: #E03131!important;
}

.header .oh{
  background: #E7AE00!important;
}
.trim {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.bdrms > span{
  /* background: #F4F7F9;
  border-radius: 1px;
  color: #839DB6; */
  font-size: 14px;
  /* padding: 1px 4px; */
  /* margin-left: 5px; */
  width: 41px;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
}
#propPreviewBottom .bdrms {
  padding: 2px 0 2px 0;
  text-align: right;
}
.bdrms .rmbed{
  width: auto;
  margin-right: 0px;
}
h1.title{
  font-size: 16px;
}
.ptype{
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
</style>
