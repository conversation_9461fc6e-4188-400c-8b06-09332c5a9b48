<template lang="pug">
#evaluation-main(v-cloak)
  #busy-icon.overlay.loader-wrapper(v-show="isBusy")
    div.loader
  header#header-bar.bar.bar-nav(v-if="!nobar")
    a.icon.fa.fa-back.pull-left(@click="goBack()")
    h1.title.ng-cloak!= "{{_('Evaluation Conditions','evaluation')}}"
  div.bar.bar-standard.bar-footer.row(@click="evaluate()")
    | {{_('Next', 'evaluation')}}
  div#evaluation.content
    div.content-list
      div.block
        div.header
          span {{_('Step 1','evaluation')}}
        div#address(@click="openMap()")
          div
            span.fa.fa-map-marker
          div.pull-right.trim(:class="{'disable' : reEval || fromMls}")
            span {{address || _('Input Address','evaluation')}}
      evaluation-hist-cnt-card(:uaddr="uaddr", :histcnt="histcnt", :disp-var="dispVar", :address="address",:prop="prop",:propcnt="propcnt")
      div#details()
        evaluation-inputs(:prop.sync="prop", @on-change="onChangeVal")
      div#keyboardPadd(style="height:24px;background:#f1f1f1;")
  div.bar.bar-standard.bar-footer.row(@click="evaluate()")
    | {{_('Next', 'evaluation')}}

  div(style="display:none")
    span(v-for="(v,k) of strings") {{_(v.key, v.ctx)}}
</template>

<script>
import EvaluationInputs from './frac/EvaluationInputs'
import rmsrv_mixins from './rmsrv_mixins'
import pagedata_mixins from './pagedata_mixins'
import evaluate_mixins from './evaluate_mixins'
import prop_mixins from './prop_mixins'
import EvaluationHistCntCard from './frac/EvaluationHistCntCard.vue';

export default {
  props:{

  },
  mixins: [rmsrv_mixins,pagedata_mixins,evaluate_mixins,prop_mixins],
  components:{
    EvaluationInputs,
    EvaluationHistCntCard,
  },
  data () {
    return {
      strings:{
        inputAddr:{key:"Please Input Address.", ctx:''},
        message:{key:"Message", ctx:'evaluation'},
        cancel:{key:"Cancel", ctx:''},
        confirm:{key:"Confirm", ctx:''}
      },
      dispVar:{
        type:Object,
        default:function () {
          return {fav:false};
        }
      },
      isBusy: true,
      back: vars.back||false,
      msg: '',
      place: {},
      mapObj: {},
      ver: null,
      calculating: false,
      bdrms: null,
      bthrms: null,
      gr: null,
      reno: 3,
      prop:{
        bdrms: 1,
        bthrms: 1,
        br_plus:null,
        tp: 'Detached',
        gr: 1,
        tax:null,
        sqft:null,
        reno: 3,
        lat:0,
        lng:0,
      },
      props:[],
      histcnt:0,
      totalhist:0,
      showProps: false,
      address:'',
      propcnt:0,
      addr:{},
      nobar: vars.nobar,
      reEval:vars.reEval,
      fromMls: vars.fromMls,
      datas: [
        'isApp',
        'isCip',
        'isLoggedIn',
        'lang',
        'sessionUser',
        'reqHost',
        'isVipUser',
        'isVipRealtor'
      ],
      uaddr: '',
    };
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    var bus = window.bus, self = this;
    this.getPageData(this.datas,{},true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      if (vars.lat||vars.addr) {
        self.init(vars);
        self.nobar = vars.nobar
      }
    });
  },
  methods: {
    onChangeVal(val){
      // console.log(val);
    },
    openMap() {
      if (vars.reEval || vars.fromMls)
        return;
      var self = this;
      // var val = {"cnty":"Canada","prov":"Québec","st":"Unnamed Road","zip":"J0X","lng":-77.4525873,"lat":46.4622392}
      // var val ={"cnty":"Canada","city":"Toronto","prov":"Ontario","st_num":"65","st":"Yonge St","zip":"M5E1Z1","lng":-79.3774654,"lat":43.6487842}
      // self.init(val);
      if (this.dispVar.isLoggedIn) {
        // window.bus.$emit('select-place', {});
        var cfg = {hide:false,title:this._('Select Address')};
        var url = "/1.5/map/searchLocation";
        if(this.prop.lat && this.prop.lng) {
          url = url +'?lat='+this.prop.lat+'&lng='+this.prop.lng;
        }
        url = this.appendDomain(url);

        RMSrv.getPageContent(url, '#callBackString', cfg, function(val) {
          if (val == ':cancel') {
            return;
          } else  {
            var ret = JSON.parse(val);
            self.init(ret);
          }
        });
    } else {
        var url = '/1.5/user/login';
        this.redirect(url);
      }
    },
    redirect(url){
      if(this.dispVar.isApp) {
        RMSrv.closeAndRedirectRoot(this.appendDomain(url));
      } else {
        return window.location = url;
      }
    },
    toggleModal(a){
      toggleModal(a);
      this.reset();
    },
    reset() {
      this.address='';
      this.propnum = 0;
      this.histcnt = 0;
      this.totalhist = 0,
      this.addr = {};
      this.prop = {
        bdrms: 1,
        bthrms: 1,
        tp: 'Detached',
        br_plus:0,
        gr: 1,
        tax:null,
        sqft:null,
        reno: 3,
        lat:0,
        lng:0,
      };
    },
    init(vars) {
      var self = this;
      this.addr = {};
      for (let key of this.fields) {
        if (vars[key]){
          if (key == 'img') {
            this.addr[key] = this.prop[key] = decodeURIComponent(vars[key])
          } else if(this.numFields.indexOf(key) >=0 ) {
            this.addr[key] = this.prop[key] = Number(vars[key]);
          } else {
            this.addr[key] = this.prop[key] = vars[key];
          }
        }
      }
      this.prop.addr = vars.addr = vars.showAddr? vars.showAddr: vars.addr? vars.addr: vars.st_num? vars.st_num + ' ' +  vars.st : vars.st ||'';
      var city = vars.city? this._(vars.city) + ',' : '';
      var addr = vars.addr? vars.addr + ',' : '';
      this.address = addr + city  + this._(vars.prov)||'';
      if (vars.unt)
        this.address = vars.unt + ' ' + this.address;
      this.getHistCnt();
      this.getPropCount();
    },
    getHistCnt() {
      let self = this;
      this.$http.post('/1.5/evaluation/histcnt',this.addr).then(
        function(ret) {
          ret = ret.data;
          if (ret.ok == 1) {
            self.prop.uaddr = ret.uaddr;
            self.uaddr = ret.uaddr;
            self.histcnt = ret.histcnt||0;
          } else {
            console.log(ret.err)
            self.msg = "error";
          }
        },function(ret){
          ajaxError(ret);
        });
    },
    getPropCount() {
      let self = this;
      this.addr.getOneProp = true;
      self.getPropCnt(this.addr, function(ret) {
        ret = ret.data;
        if (ret.ok == 1) {
          self.propcnt = ret.propcnt;
          self.prop.uaddr = ret.uaddr;
          if (!self.uaddr && ret.uaddr) {
            self.uaddr = ret.uaddr;
          }
          if (!self.prop.mlsid) {
            var prop = ret.prop;
            if(prop && !vars.bdrms) {
              var ptype2;
              if (prop.tp) {
                self.prop.tp = prop.tp;
              } else if (prop.ptype2) {
                var types = ['Apartment','Townhouse', 'Detached','Semi-Detached']
                for(let i of types) {
                  if(prop.ptype2.indexOf(i) >=0)
                  self.prop.tp = i;
                }
              }
              self.prop.bdrms = prop.bdrms;
              self.prop.bthrms = prop.bthrms;
              self.prop.gr = prop.gr;
              if (prop.tax)
                self.prop.tax = prop.tax;
              self.prop.sqft = self.getPropSqft(prop);
            }
          }
        } else {
          console.log(ret.err)
        }
      });
    },
    evaluate (){
      // console.log(this.prop);
      // return;
      if (!this.prop.lng && !this.prop.lat) {
        RMSrv.dialogAlert(this._(this.strings.inputAddr.key) );
        return;
      }
      var url = '/1.5/evaluation/comparables.html?nobar=1&inframe=1&' + this.buildUrlFromProp();
      if (vars.fromMls) {
        url +='&fromMls=1'
      }
      // document.location.href= url;
      url = RMSrv.appendDomain(url);
      var cfg = {hide:false, title:this._('Evaluation Conditions','evaluation')}
      document.location.href = url;
      // RMSrv.getPageContent(url, '#callBackString', cfg, function(val) {
      //   if (vars.fromMls) {
      //     // window.rmCall(':ctx:close');
      //   }
      // });
    },
  }
}

</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.hist{
  padding: 10px;
  height: 48px;
  font-size: 14px;
  border-top: 1px solid #f1f1f1;
  color:#999;
  line-height: 48px;
  display: flex;
  align-items: center;
}
.hist .icon {
  font-size: 21px;
  padding: 10px;
  color: #999;
}
.hist .num {
  color: #e03131;
  padding-right: 5px;
}
.hist .icon-right-nav {
  font-size: 12px;
  margin-left: auto;
}
.hist .user-img {
  width: 21px;
  margin: 10px;
  float: left;
  border-radius: 50%;
  height: 21px;
  background-size: 100% 100%;
  background-color: #eaebec;
  background-image: url(/img/user-icon-placeholder.png)
}
#address .disable {
  color: #9b9b9b!important;
}
/* .props{
  position: absolute;
  top: 0;
  overflow: auto;
  height: 100%;
  width: 100%;
  z-index: 20;
  background: white;
}
.valign {
  position: absolute;
  padding-right: 20px;
  top: 50%;
  transform: translate(0, -50%);
} */
h4 .fa {
  padding: 0px 10px 10px 10px;
  font-size: 20px;
}

#evaluation {
  padding-bottom: 50px;
  /* overflow-y: auto; */
  background: #f1f1f1;
  color: #9b9b9b;
}
#evaluation h4 {
  padding-left: 5px;
  line-height: 2;
}
[v-cloak] {
  display: none;
}

#input-box {
  margin: 0 10px;
}
#address .fa {
  font-size: 20px;
  padding-right: 20px;
}
#address {
  width: 100%;
  padding: 10px;
  font-size: 12px;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 100%;
  display: flex;
  align-items: center;
  height: 66px;
  background: white;
  padding:20px;
  justify-content: space-between;
}
#address .pull-right {
  font-size: 17px;
  color: black;
}
.bar-footer {
  text-align: center;
  vertical-align: middle;
  line-height: 50px;
  height: 50px;
  color: white;
  background-color: #5cb85c;
  z-index: 1;
  border: none;
  /* position:absolute; */
}
/* #evaluation{ */
#evaluation-main{
  /* -webkit- */
  /* transform: translate3d(0, 0, 0); */
}
.content>*{
  transform: none!important;
}
</style>
