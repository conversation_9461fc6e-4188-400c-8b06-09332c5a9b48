<template lang="pug">
div
  header.bar.bar-nav(style="padding-right:0px;", v-if="!noTitleBar && items.length")
    h1.title {{_("RealMaster")}} {{items.length}} {{_('Results')}}
  div#list-container.content(v-show="items.length && mode == 'list'")
    prop-list(:list.sync='items', :disp-var="dispVar")
    user-brief-info(:owner.sync="dispVar.ownerData", :qrcd.sync="qrcd")
    disclaimer(:no-bot-bar="false")
    //- div(v-if="dispVar.ownerData.eml")
    //-   wechat-qr-modal(:owner.sync="dispVar.ownerData", :qrcd.sync="qrcd")

  //- div(v-if="fromShare && wDl")
  //-   get-app-bar(:dtl-in-app="true", :w-dl.sync="wDl", :owner.sync="dispVar.ownerData", :params="params")
  div#propDetailModal(v-show="mode=='detail'")
    prop-detail(:w-id="wId", :owner.sync="dispVar.ownerData", :user-form="userForm", :btn-close='items.length > 0', :from-share="true", :in-frame="inFrame", :no-title-bar="noTitleBar", :signup-title="signupTitle")
  prop-detail-map-menu(:prop.sync="prop", :disp-var="dispVar")

  div.WSBridge(style="display:none", v-if="!items.length")
    listing-share-desc(:prop.sync="prop", :is-app.sync="dispVar.isApp")
    span#share-url {{fullUrl}}
    span#wx-url {{fullUrl}}
    div#share-image {{shareImage}}
    div#wx-image {{shareImage}}

  div.WSBridge(style="display:none", v-if="items.length")
    div#wx-title {{_('RealMaster')}} {{items.length}} {{_('Results')}}
    div#wx-desc
      span(v-if="dispVar.ownerData.eml")
        | {{_('Shared by')}} {{dispVar.ownerData.fn?dispVar.ownerData.fn+dispVar.ownerData.ln:dispVar.ownerData.nm}},{{dispVar.ownerData.mbl}},{{dispVar.ownerData.eml}}
      span(v-else)
        | {{_('RealMaster Multiple Properties Sharing')}}
    div#wx-image
      span(v-if="dispVar.ownerData.eml")
        | {{dispVar.ownerData.avt || '/img/logo.png'}}
      span(v-else)
        | /img/logo.png
    div#wx-url {{fullUrl}}
    div#share-image {{shareImage}}
    div#share-data {{shareData}}

  div#schoolDetailModal.modal(:class="")
    school-detail(:disp-var="dispVar")
  get-app-bar(:dtl-in-app="true", :w-dl.sync="wDl", :owner.sync="dispVar.ownerData", :params="params", v-show="wDl && !inFrame")
</template>

<script>
import PropDetail from './frac/PropDetail.vue'
import GetAppBar from './frac/GetAppBar.vue'
import ListingShareDesc from './frac/ListingShareDesc.vue'
import pagedata_mixins from './pagedata_mixins'
import prop_mixins from './prop_mixins'

import PropList  from './frac/PropList.vue'
import Disclaimer  from './frac/Disclaimer.vue'
import UserBriefInfo from './frac/UserBriefInfo.vue'
import SchoolDetail from './frac/SchoolDetail.vue'
import PropDetailMapMenu from './frac/PropDetailMapMenu.vue'

// import filters from '../components/filters'

// import rmsrv_mixins from './rmsrv_mixins'
export default {
  // filters:{
  //   currency:filters.currency,
  // },
  mixins: [pagedata_mixins,prop_mixins],//rmsrv_mixins],
  data () {
    return {
      dispVar:{
        isApp:false,
        ownerData:{vip:false},
        isRealtor:false
      },
      mode:        'detail',
      fullUrl:     document.URL,
      dis:         true,
      prop:        {img:''},
      qrcd:        false,
      wId:         false,
      inFrame:     false,
      noTitleBar:  false,
      schVueClick: false, //just jump page
      userForm :   {sid:null, tp:'prop', formid:'system',url:document.URL},
      wDl:         false,
      id:          vars.id?[vars.id]:[],
      wSign:       true,
      loading: false,
      items:       [],
      locale:        'zh-cn',
      signupTitle: null,
      datas:[
        'defaultEmail',
        'isCip',
        'isApp',
        'isLoggedIn',
        'jsCordova',
        'wxConfig',
        'jsWechat',
        // 'jsGmapUrl',
        'ownerData',
        'lang',
        'isRealtor'
      ],
      datasObj:{
        src:'detail',
        ownerId:'',
        url:document.URL
      }
    };
  },
  computed:{
    shareData:function(){
      return 'id='+this.id;
    },
    shareImage: function () {
      return this.prop.thumbUrl ? this.prop.thumbUrl : '/img/create_exlisting.png';
    },
    params: function () {
      return this.id.length? "?m_ids="+this.id.join(',')  : '';
    },
  },
  beforeMount (){
    if (vars.lang) {
      this.locale = vars.lang;
    }
    this.wDl = vars.wDl?true:false;
    if (vars.aid) {
      this.wId = true;
      this.datasObj.ownerId = vars.aid;
    } else if (vars.uid != 'null') {
      // this.wDl = true;
      if (vars.wSign) {
        this.wId = true
      }
      this.datasObj.ownerId = vars.uid;
    }
    // if (vars.ownerid) {
    //   this.wId = true;
    //   this.datasObj.ownerId = vars.ownerid;
    // }
    if (vars.prop && vars.prop._id) {
      this.prop = Object.assign(this.prop,vars.prop);
      // this.prop.img = this.picUrl(prop);
    }
    this.inFrame = (vars.inframe || vars.inFrame)?true:false;
    this.noTitleBar = vars.nobar?true:false;
    if (this.inframe) {
      this.datas = [
        'defaultEmail',
        'isCip',
        'isApp',
        // 'jsGmapUrl',
        'ownerData',
        'jsCordova'
      ];
      this.datasObj.inFrame = true;
    }
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    if (!window.gMapsCallback) {
      window.gMapsCallback = this.initGmap;
    }
    var bus = window.bus, self = this;
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      if (d.ownerData && d.ownerData._id) {
        self.userForm.uid = d.ownerData._id;
        if (!d.ownerData.vip) {
          self.wDl = true;
        }
      }
    });
    bus.$on('school-retrieved', function (sch) {
      toggleModal('schoolDetailModal','open');
    })
    bus.$on('prop-retrived', function (prop) {
      self.mode = 'detail';
      self.prop = prop;
      self.formatedAddr = `${prop.showAddr||prop.addr}, ${prop.city_en||prop.city}`
      // var id = 'share-title';
      // if (self.lang == 'en') {
      //   id = "share-title-en";
      // }
      // TODO: no contact when outside app
      // if (prop.rmcontact && !self.wId) {
      //   self.wId = true;
      //   self.signupTitle = self._('Contact Realtor');
      //   self.userForm.ueml = self.dispVar.defaultEmail;
      //   setTimeout(function () {
      //     console.log(self.dispVar.ownerData);
      //     self.dispVar.ownerData.vip = true;
      //   }, 10);
      // }
      // setTimeout(function () {
      //   if (document.getElementById(id)) {
      //     let title = document.getElementById(id).textContent;
      //     document.title = title;
      //     document.querySelector("meta[property='og:title']").setAttribute('content', title);
      //   }
      //   if (document.getElementById('share-image')) {
      //     let image = document.getElementById('share-image').textContent;
      //     document.querySelector("meta[property='og:image']").setAttribute('content', image);
      //   }
      //
      // }, 200);
    });
    bus.$on('prop-detail-close', function (prop) {
      self.mode = 'list';
    });
    if (!self.datasObj.ownerId) {
      delete self.datasObj.ownerId;
      // self.datas.pop();
      self.datas.splice(self.datas.indexOf('ownerData'),1)
    }
    if (vars.ec && !(vars.prop && vars.prop.picUrls)) {
      this.processEcData(vars.ec);
    } else {
      if (vars.id && vars.id.indexOf('.') > 0) {
        var id = vars.id.split('.')[0];
        this.id =self.userForm.id =  id;
        // if (/^RM/.test(id)) {
        //   self.userForm.rmid = id;
        // } else {
        //   self.userForm.sid = id;
        // }
        this.datasObj.ownerId = vars.id.split('.')[1];
        this.wId = true;
      }
      this.initData();
    }
    self.getPageData(self.datas, self.datasObj, true);
  },
  components: {
    PropDetail,
    GetAppBar,
    ListingShareDesc,
    PropList,
    Disclaimer,
    UserBriefInfo,
    SchoolDetail,
    PropDetailMapMenu
  },
  methods: {
    initData(){
      var self = this;
      self.userForm.id = self.id[0];
      if (/^RM/.test(self.id[0])) {
        self.userForm.rmid = self.id[0];
      } else {
        self.userForm.sid = self.id[0];
      }
      if (self.id.length > 1) {
        self.mode = 'list';
        self.getItems();
      } else {
        var prop = {_id:self.id[0], locale:self.locale};
        if (vars.prop && vars.prop._id) {
          prop = vars.prop;
          self.userForm.id = prop._id;
          self.userForm.sid = prop.sid;
          self.userForm.addr = `${prop.unt?(prop.unt+' '):''}${prop.addr}`;
          self.userForm.city = prop.city_en;
          self.userForm.prov = prop.prov_en;
        }
        window.bus.$emit('prop-changed', prop);
      }
    },
    getItems(){
      var self = this;
      self.$http.post('/1.5/search/prop/list', {id:self.id.join(','), share:true, locale:self.locale, limit:40}).then(
        function(ret) {
          ret = ret.data;
          self.loading = false;
          if (ret.e) {
            console.error(ret.e);
            self.err = ret.e;
            return;
          }
          self.err = '';
          if (ret.resultList) {
            self.items = self.items.concat(ret.resultList);
          }
          self.initPropListImg();
        }, function() {
          ajaxError(ret);
        });
    },
    initGmap () {
      var map = new google.maps.Map(document.getElementById('detail-map-holder'), {
          center: {lat: -34.397, lng: 150.644},
          zoom: 8
      });
    },
    initPropListImg () {
      for (let prop of this.items) {
        if (!prop.thumbUrl) {
          prop.thumbUrl = this.picUrl(prop);
        }
      }
    },
    isRMProp (prop) {
      return /^RM/.test(prop.id);
    },
    picUrl (r) {
      var ret = this.setupThisPicUrls(r);
      return ret[0] || (window.location.origin + "/img/noPic.png");
      // var ret;
      // if (this.isRMProp(r)) {
      //   r.pic.ml_num = r.sid || r.ml_num;
      //   ret = this.$parent.convert_rm_imgs(this, r.pic, 'reset');
      //   return ret[0] || (window.location.origin + "/img/noPic.png");
      // } else {
      //   return listingPicUrls(r, this.dispVar.isCip)[0] || '/img/noPic.png';
      // }
    },
    processEcData(ec){
      var self = this;
      self.$http.post('/mp/decData', {ec:ec}).then(
        function (ret) {
          ret = ret.data;
          if (ret.e) {
            if (ret.ec == 2) {
              window.location = ret.url;
            }
            RMSrv.dialogAlert(ret.e);
          } else {
            if (ret.id.indexOf(',') > 0) {
              self.id = ret.id.split(',');
            } else {
              self.id = [ret.id];
            }
            self.initData();
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    showSchool (_id) {
    },
    openTBrowser (url) {
      window.location = url;
    },
    showInBrowser(url){
      window.location = url;
    },
  }
}
</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
#list-container{
  background: #f1f1f1;
}
</style>
